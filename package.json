{"name": "contentia-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "node server.js", "lint": "next lint", "deploy": "npm install && npm run build && npm run start"}, "dependencies": {"@headlessui/react": "^2.1.8", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^4.1.3", "@hotjar/browser": "^1.0.9", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@reduxjs/toolkit": "^2.2.8", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-placeholder": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/react": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "antd": "^5.21.6", "apexcharts": "^3.54.1", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dompurify": "^3.2.4", "i18next": "^23.15.1", "jwt-decode": "^4.0.0", "lucide-react": "^0.474.0", "mixpanel-browser": "^2.65.0", "next": "^15.3.1", "next-i18next": "^15.3.1", "react": "^18", "react-apexcharts": "^1.5.0", "react-data-table-component": "^7.6.2", "react-dom": "^18", "react-ga4": "^2.1.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.0.2", "react-icons": "^5.3.0", "react-number-format": "^5.4.2", "react-redux": "^9.1.2", "react-slick": "^0.30.2", "react-toastify": "^10.0.6", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/jwt-decode": "^3.1.0", "@types/mixpanel-browser": "^2.60.0", "@types/node": "^20", "@types/react": "^18.3.11", "@types/react-dom": "^18", "@types/react-slick": "^0.23.13", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.6.3"}}
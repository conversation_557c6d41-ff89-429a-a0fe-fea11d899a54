commit 13bd5dc6e66267374e843240119b368607ac8e12
Author: <PERSON><PERSON><PERSON><PERSON>bpk <<EMAIL>>
Date:   Thu May 15 16:33:27 2025 +0500

    Text Editor

diff --git a/next.config.js b/next.config.js
index 226ed50..b17060a 100644
--- a/next.config.js
+++ b/next.config.js
@@ -1,6 +1,6 @@
 /** @type {import('next').NextConfig} */
 const nextConfig = {
-  reactStrictMode: true,
+  // reactStrictMode: true,
   // Generate a unique build ID to prevent caching issues
   generateBuildId: () => 'build-' + Date.now(),
   // Add build timestamp as an environment variable
@@ -24,6 +24,19 @@ const nextConfig = {
       },
     ]
   },
+  // // Add this webpack configuration to fix redux-persist issues
+  // webpack: (config, { isServer }) => {
+  //   if (!isServer) {
+  //     // Don't resolve 'fs' module on the client to prevent errors
+  //     config.resolve.fallback = {
+  //       ...config.resolve.fallback,
+  //       fs: false,
+  //       net: false,
+  //       tls: false,
+  //     };
+  //   }
+  //   return config;
+  // },
 }
 
-module.exports = nextConfig
+module.exports = nextConfig
\ No newline at end of file
diff --git a/package-lock.json b/package-lock.json
index 81bd8ae..40c9e51 100644
--- a/package-lock.json
+++ b/package-lock.json
@@ -26,6 +26,7 @@
         "lucide-react": "^0.474.0",
         "next": "^15.3.1",
         "next-i18next": "^15.3.1",
+        "quill": "^2.0.3",
         "react": "^18",
         "react-apexcharts": "^1.5.0",
         "react-data-table-component": "^7.6.2",
@@ -1809,16 +1810,44 @@
       }
     },
     "node_modules/call-bind": {
-      "version": "1.0.7",
-      "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.7.tgz",
-      "integrity": "sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==",
+      "version": "1.0.8",
+      "resolved": "https://registry.npmjs.org/call-bind/-/call-bind-1.0.8.tgz",
+      "integrity": "sha512-oKlSFMcMwpUg2ednkhQ454wfWiU/ul3CkJe/PEHcTKuiX6RpbehUiFMXu13HalGZxfUwCQzZG747YXBn1im9ww==",
       "license": "MIT",
       "dependencies": {
+        "call-bind-apply-helpers": "^1.0.0",
         "es-define-property": "^1.0.0",
-        "es-errors": "^1.3.0",
-        "function-bind": "^1.1.2",
         "get-intrinsic": "^1.2.4",
-        "set-function-length": "^1.2.1"
+        "set-function-length": "^1.2.2"
+      },
+      "engines": {
+        "node": ">= 0.4"
+      },
+      "funding": {
+        "url": "https://github.com/sponsors/ljharb"
+      }
+    },
+    "node_modules/call-bind-apply-helpers": {
+      "version": "1.0.2",
+      "resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz",
+      "integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==",
+      "license": "MIT",
+      "dependencies": {
+        "es-errors": "^1.3.0",
+        "function-bind": "^1.1.2"
+      },
+      "engines": {
+        "node": ">= 0.4"
+      }
+    },
+    "node_modules/call-bound": {
+      "version": "1.0.4",
+      "resolved": "https://registry.npmjs.org/call-bound/-/call-bound-1.0.4.tgz",
+      "integrity": "sha512-+ys997U96po4Kx/ABpBCqhA9EuxJaQWDQg7295H4hBphv3IZg0boBKuwYpt4YXp6MZ5AmZQnU/tyMTlRpaSejg==",
+      "license": "MIT",
+      "dependencies": {
+        "call-bind-apply-helpers": "^1.0.2",
+        "get-intrinsic": "^1.3.0"
       },
       "engines": {
         "node": ">= 0.4"
@@ -2214,6 +2243,20 @@
         "@types/trusted-types": "^2.0.7"
       }
     },
+    "node_modules/dunder-proto": {
+      "version": "1.0.1",
+      "resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz",
+      "integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==",
+      "license": "MIT",
+      "dependencies": {
+        "call-bind-apply-helpers": "^1.0.1",
+        "es-errors": "^1.3.0",
+        "gopd": "^1.2.0"
+      },
+      "engines": {
+        "node": ">= 0.4"
+      }
+    },
     "node_modules/eastasianwidth": {
       "version": "0.2.0",
       "resolved": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz",
@@ -2255,13 +2298,10 @@
       "license": "MIT"
     },
     "node_modules/es-define-property": {
-      "version": "1.0.0",
-      "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz",
-      "integrity": "sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==",
+      "version": "1.0.1",
+      "resolved": "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz",
+      "integrity": "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g==",
       "license": "MIT",
-      "dependencies": {
-        "get-intrinsic": "^1.2.4"
-      },
       "engines": {
         "node": ">= 0.4"
       }
@@ -2275,10 +2315,22 @@
         "node": ">= 0.4"
       }
     },
+    "node_modules/es-object-atoms": {
+      "version": "1.1.1",
+      "resolved": "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz",
+      "integrity": "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA==",
+      "license": "MIT",
+      "dependencies": {
+        "es-errors": "^1.3.0"
+      },
+      "engines": {
+        "node": ">= 0.4"
+      }
+    },
     "node_modules/eventemitter3": {
-      "version": "2.0.3",
-      "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz",
-      "integrity": "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==",
+      "version": "5.0.1",
+      "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-5.0.1.tgz",
+      "integrity": "sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==",
       "license": "MIT"
     },
     "node_modules/extend": {
@@ -2288,9 +2340,9 @@
       "license": "MIT"
     },
     "node_modules/fast-diff": {
-      "version": "1.1.2",
-      "resolved": "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz",
-      "integrity": "sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==",
+      "version": "1.3.0",
+      "resolved": "https://registry.npmjs.org/fast-diff/-/fast-diff-1.3.0.tgz",
+      "integrity": "sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==",
       "license": "Apache-2.0"
     },
     "node_modules/fast-glob": {
@@ -2425,16 +2477,21 @@
       }
     },
     "node_modules/get-intrinsic": {
-      "version": "1.2.4",
-      "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz",
-      "integrity": "sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==",
+      "version": "1.3.0",
+      "resolved": "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz",
+      "integrity": "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ==",
       "license": "MIT",
       "dependencies": {
+        "call-bind-apply-helpers": "^1.0.2",
+        "es-define-property": "^1.0.1",
         "es-errors": "^1.3.0",
+        "es-object-atoms": "^1.1.1",
         "function-bind": "^1.1.2",
-        "has-proto": "^1.0.1",
-        "has-symbols": "^1.0.3",
-        "hasown": "^2.0.0"
+        "get-proto": "^1.0.1",
+        "gopd": "^1.2.0",
+        "has-symbols": "^1.1.0",
+        "hasown": "^2.0.2",
+        "math-intrinsics": "^1.1.0"
       },
       "engines": {
         "node": ">= 0.4"
@@ -2443,6 +2500,19 @@
         "url": "https://github.com/sponsors/ljharb"
       }
     },
+    "node_modules/get-proto": {
+      "version": "1.0.1",
+      "resolved": "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz",
+      "integrity": "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g==",
+      "license": "MIT",
+      "dependencies": {
+        "dunder-proto": "^1.0.1",
+        "es-object-atoms": "^1.0.0"
+      },
+      "engines": {
+        "node": ">= 0.4"
+      }
+    },
     "node_modules/glob": {
       "version": "10.4.5",
       "resolved": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz",
@@ -2476,12 +2546,12 @@
       }
     },
     "node_modules/gopd": {
-      "version": "1.0.1",
-      "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz",
-      "integrity": "sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==",
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz",
+      "integrity": "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg==",
       "license": "MIT",
-      "dependencies": {
-        "get-intrinsic": "^1.1.3"
+      "engines": {
+        "node": ">= 0.4"
       },
       "funding": {
         "url": "https://github.com/sponsors/ljharb"
@@ -2499,22 +2569,10 @@
         "url": "https://github.com/sponsors/ljharb"
       }
     },
-    "node_modules/has-proto": {
-      "version": "1.0.3",
-      "resolved": "https://registry.npmjs.org/has-proto/-/has-proto-1.0.3.tgz",
-      "integrity": "sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==",
-      "license": "MIT",
-      "engines": {
-        "node": ">= 0.4"
-      },
-      "funding": {
-        "url": "https://github.com/sponsors/ljharb"
-      }
-    },
     "node_modules/has-symbols": {
-      "version": "1.0.3",
-      "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz",
-      "integrity": "sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz",
+      "integrity": "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ==",
       "license": "MIT",
       "engines": {
         "node": ">= 0.4"
@@ -2608,13 +2666,13 @@
       }
     },
     "node_modules/is-arguments": {
-      "version": "1.1.1",
-      "resolved": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.1.1.tgz",
-      "integrity": "sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==",
+      "version": "1.2.0",
+      "resolved": "https://registry.npmjs.org/is-arguments/-/is-arguments-1.2.0.tgz",
+      "integrity": "sha512-7bVbi0huj/wrIAOzb8U1aszg9kdi3KN/CyU19CTI7tAoZYEZoL9yCDXpbXN+uPsuWnP02cyug1gleqq+TU+YCA==",
       "license": "MIT",
       "dependencies": {
-        "call-bind": "^1.0.2",
-        "has-tostringtag": "^1.0.0"
+        "call-bound": "^1.0.2",
+        "has-tostringtag": "^1.0.2"
       },
       "engines": {
         "node": ">= 0.4"
@@ -2658,12 +2716,13 @@
       }
     },
     "node_modules/is-date-object": {
-      "version": "1.0.5",
-      "resolved": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.0.5.tgz",
-      "integrity": "sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==",
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/is-date-object/-/is-date-object-1.1.0.tgz",
+      "integrity": "sha512-PwwhEakHVKTdRNVOw+/Gyh0+MzlCl4R6qKvkhuvLtPMggI1WAHt9sOwZxQLSGpUaDnrdyDsomoRgNnCfKNSXXg==",
       "license": "MIT",
       "dependencies": {
-        "has-tostringtag": "^1.0.0"
+        "call-bound": "^1.0.2",
+        "has-tostringtag": "^1.0.2"
       },
       "engines": {
         "node": ">= 0.4"
@@ -2712,13 +2771,15 @@
       }
     },
     "node_modules/is-regex": {
-      "version": "1.1.4",
-      "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.1.4.tgz",
-      "integrity": "sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==",
+      "version": "1.2.1",
+      "resolved": "https://registry.npmjs.org/is-regex/-/is-regex-1.2.1.tgz",
+      "integrity": "sha512-MjYsKHO5O7mCsmRGxWcLWheFqN9DJ/2TmngvjKXihe6efViPqc274+Fx/4fYj/r03+ESvBdTXK0V6tA3rgez1g==",
       "license": "MIT",
       "dependencies": {
-        "call-bind": "^1.0.2",
-        "has-tostringtag": "^1.0.0"
+        "call-bound": "^1.0.2",
+        "gopd": "^1.2.0",
+        "has-tostringtag": "^1.0.2",
+        "hasown": "^2.0.2"
       },
       "engines": {
         "node": ">= 0.4"
@@ -2809,6 +2870,12 @@
       "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==",
       "license": "MIT"
     },
+    "node_modules/lodash-es": {
+      "version": "4.17.21",
+      "resolved": "https://registry.npmjs.org/lodash-es/-/lodash-es-4.17.21.tgz",
+      "integrity": "sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==",
+      "license": "MIT"
+    },
     "node_modules/lodash.castarray": {
       "version": "4.4.0",
       "resolved": "https://registry.npmjs.org/lodash.castarray/-/lodash.castarray-4.4.0.tgz",
@@ -2816,12 +2883,25 @@
       "dev": true,
       "license": "MIT"
     },
+    "node_modules/lodash.clonedeep": {
+      "version": "4.5.0",
+      "resolved": "https://registry.npmjs.org/lodash.clonedeep/-/lodash.clonedeep-4.5.0.tgz",
+      "integrity": "sha512-H5ZhCF25riFd9uB5UCkVKo61m3S/xZk1x4wA6yp/L3RFP6Z/eHH1ymQcGLo7J3GMPfm0V/7m1tryHuGVxpqEBQ==",
+      "license": "MIT"
+    },
     "node_modules/lodash.debounce": {
       "version": "4.0.8",
       "resolved": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz",
       "integrity": "sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==",
       "license": "MIT"
     },
+    "node_modules/lodash.isequal": {
+      "version": "4.5.0",
+      "resolved": "https://registry.npmjs.org/lodash.isequal/-/lodash.isequal-4.5.0.tgz",
+      "integrity": "sha512-pDo3lu8Jhfjqls6GkMgpahsF9kCyayhgykjyLMNFTKWrpVdAQtYyB4muAMWozBB4ig/dtWAmsMxLEI8wuz+DYQ==",
+      "deprecated": "This package is deprecated. Use require('node:util').isDeepStrictEqual instead.",
+      "license": "MIT"
+    },
     "node_modules/lodash.isplainobject": {
       "version": "4.0.6",
       "resolved": "https://registry.npmjs.org/lodash.isplainobject/-/lodash.isplainobject-4.0.6.tgz",
@@ -2863,6 +2943,15 @@
         "react": "^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0"
       }
     },
+    "node_modules/math-intrinsics": {
+      "version": "1.1.0",
+      "resolved": "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz",
+      "integrity": "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g==",
+      "license": "MIT",
+      "engines": {
+        "node": ">= 0.4"
+      }
+    },
     "node_modules/merge2": {
       "version": "1.4.1",
       "resolved": "https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz",
@@ -3407,33 +3496,40 @@
       "license": "MIT"
     },
     "node_modules/quill": {
-      "version": "1.3.7",
-      "resolved": "https://registry.npmjs.org/quill/-/quill-1.3.7.tgz",
-      "integrity": "sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==",
+      "version": "2.0.3",
+      "resolved": "https://registry.npmjs.org/quill/-/quill-2.0.3.tgz",
+      "integrity": "sha512-xEYQBqfYx/sfb33VJiKnSJp8ehloavImQ2A6564GAbqG55PGw1dAWUn1MUbQB62t0azawUS2CZZhWCjO8gRvTw==",
       "license": "BSD-3-Clause",
       "dependencies": {
-        "clone": "^2.1.1",
-        "deep-equal": "^1.0.1",
-        "eventemitter3": "^2.0.3",
-        "extend": "^3.0.2",
-        "parchment": "^1.1.4",
-        "quill-delta": "^3.6.2"
+        "eventemitter3": "^5.0.1",
+        "lodash-es": "^4.17.21",
+        "parchment": "^3.0.0",
+        "quill-delta": "^5.1.0"
+      },
+      "engines": {
+        "npm": ">=8.2.3"
       }
     },
     "node_modules/quill-delta": {
-      "version": "3.6.3",
-      "resolved": "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz",
-      "integrity": "sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==",
+      "version": "5.1.0",
+      "resolved": "https://registry.npmjs.org/quill-delta/-/quill-delta-5.1.0.tgz",
+      "integrity": "sha512-X74oCeRI4/p0ucjb5Ma8adTXd9Scumz367kkMK5V/IatcX6A0vlgLgKbzXWy5nZmCGeNJm2oQX0d2Eqj+ZIlCA==",
       "license": "MIT",
       "dependencies": {
-        "deep-equal": "^1.0.1",
-        "extend": "^3.0.2",
-        "fast-diff": "1.1.2"
+        "fast-diff": "^1.3.0",
+        "lodash.clonedeep": "^4.5.0",
+        "lodash.isequal": "^4.5.0"
       },
       "engines": {
-        "node": ">=0.10"
+        "node": ">= 12.0.0"
       }
     },
+    "node_modules/quill/node_modules/parchment": {
+      "version": "3.0.0",
+      "resolved": "https://registry.npmjs.org/parchment/-/parchment-3.0.0.tgz",
+      "integrity": "sha512-HUrJFQ/StvgmXRcQ1ftY6VEZUq3jA2t9ncFN4F84J/vN0/FPpQF+8FKXb3l6fLces6q0uOHj6NJn+2xvZnxO6A==",
+      "license": "BSD-3-Clause"
+    },
     "node_modules/rc-cascader": {
       "version": "3.28.2",
       "resolved": "https://registry.npmjs.org/rc-cascader/-/rc-cascader-3.28.2.tgz",
@@ -4186,6 +4282,46 @@
         "react-dom": "^16 || ^17 || ^18"
       }
     },
+    "node_modules/react-quill/node_modules/eventemitter3": {
+      "version": "2.0.3",
+      "resolved": "https://registry.npmjs.org/eventemitter3/-/eventemitter3-2.0.3.tgz",
+      "integrity": "sha512-jLN68Dx5kyFHaePoXWPsCGW5qdyZQtLYHkxkg02/Mz6g0kYpDx4FyP6XfArhQdlOC4b8Mv+EMxPo/8La7Tzghg==",
+      "license": "MIT"
+    },
+    "node_modules/react-quill/node_modules/fast-diff": {
+      "version": "1.1.2",
+      "resolved": "https://registry.npmjs.org/fast-diff/-/fast-diff-1.1.2.tgz",
+      "integrity": "sha512-KaJUt+M9t1qaIteSvjc6P3RbMdXsNhK61GRftR6SNxqmhthcd9MGIi4T+o0jD8LUSpSnSKXE20nLtJ3fOHxQig==",
+      "license": "Apache-2.0"
+    },
+    "node_modules/react-quill/node_modules/quill": {
+      "version": "1.3.7",
+      "resolved": "https://registry.npmjs.org/quill/-/quill-1.3.7.tgz",
+      "integrity": "sha512-hG/DVzh/TiknWtE6QmWAF/pxoZKYxfe3J/d/+ShUWkDvvkZQVTPeVmUJVu1uE6DDooC4fWTiCLh84ul89oNz5g==",
+      "license": "BSD-3-Clause",
+      "dependencies": {
+        "clone": "^2.1.1",
+        "deep-equal": "^1.0.1",
+        "eventemitter3": "^2.0.3",
+        "extend": "^3.0.2",
+        "parchment": "^1.1.4",
+        "quill-delta": "^3.6.2"
+      }
+    },
+    "node_modules/react-quill/node_modules/quill-delta": {
+      "version": "3.6.3",
+      "resolved": "https://registry.npmjs.org/quill-delta/-/quill-delta-3.6.3.tgz",
+      "integrity": "sha512-wdIGBlcX13tCHOXGMVnnTVFtGRLoP0imqxM696fIPwIf5ODIYUHIvHbZcyvGlZFiFhK5XzDC2lpjbxRhnM05Tg==",
+      "license": "MIT",
+      "dependencies": {
+        "deep-equal": "^1.0.1",
+        "extend": "^3.0.2",
+        "fast-diff": "1.1.2"
+      },
+      "engines": {
+        "node": ">=0.10"
+      }
+    },
     "node_modules/react-redux": {
       "version": "9.1.2",
       "resolved": "https://registry.npmjs.org/react-redux/-/react-redux-9.1.2.tgz",
@@ -4291,14 +4427,16 @@
       "license": "MIT"
     },
     "node_modules/regexp.prototype.flags": {
-      "version": "1.5.3",
-      "resolved": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.3.tgz",
-      "integrity": "sha512-vqlC04+RQoFalODCbCumG2xIOvapzVMHwsyIGM/SIE8fRhFFsXeH8/QQ+s0T0kDAhKc4k30s73/0ydkHQz6HlQ==",
+      "version": "1.5.4",
+      "resolved": "https://registry.npmjs.org/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz",
+      "integrity": "sha512-dYqgNSZbDwkaJ2ceRd9ojCGjBq+mOm9LmtXnAnEGyHhN/5R7iDW2TRw3h+o/jCFxus3P2LfWIIiwowAjANm7IA==",
       "license": "MIT",
       "dependencies": {
-        "call-bind": "^1.0.7",
+        "call-bind": "^1.0.8",
         "define-properties": "^1.2.1",
         "es-errors": "^1.3.0",
+        "get-proto": "^1.0.1",
+        "gopd": "^1.2.0",
         "set-function-name": "^2.0.2"
       },
       "engines": {
diff --git a/package.json b/package.json
index 91a95c0..4d6b89c 100644
--- a/package.json
+++ b/package.json
@@ -27,6 +27,7 @@
     "lucide-react": "^0.474.0",
     "next": "^15.3.1",
     "next-i18next": "^15.3.1",
+    "quill": "^2.0.3",
     "react": "^18",
     "react-apexcharts": "^1.5.0",
     "react-data-table-component": "^7.6.2",
diff --git a/src/app/forgot-password/page.tsx b/src/app/forgot-password/page.tsx
index 21eb4ad..9a03141 100644
--- a/src/app/forgot-password/page.tsx
+++ b/src/app/forgot-password/page.tsx
@@ -57,7 +57,7 @@ const ForgotPasswordPage = () => {
             </h2>
 
             <p className="text-center text-gray-600 mb-8">
-              {submitted
+              {submitted 
                 ? 'E-posta adresinize ??ifre s??f??rlama ba??lant??s?? g??nderdik.'
                 : 'Kay??tl?? e-posta adresini girin, ??ifre yenileme ad??mlar??n?? takip edin.'}
             </p>
diff --git a/src/components/admin/blogs/new-blogs/NewBlogs.tsx b/src/components/admin/blogs/new-blogs/NewBlogs.tsx
index 3f53a23..498ea0a 100644
--- a/src/components/admin/blogs/new-blogs/NewBlogs.tsx
+++ b/src/components/admin/blogs/new-blogs/NewBlogs.tsx
@@ -1,15 +1,12 @@
 "use client";
 import React, { useState, useCallback } from "react";
 import { useForm, Controller } from "react-hook-form";
-import dynamic from "next/dynamic";
-import "react-quill/dist/quill.snow.css";
 import { useDispatch } from "react-redux";
 import { AppDispatch } from "@/store/store";
 import { createBlog } from "@/store/features/admin/blogSlice";
 import { toast } from "react-toastify";
 import { BlogInterface } from "@/types/interfaces";
-
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 export default function NewBlogs() {
     const dispatch = useDispatch<AppDispatch>();
@@ -182,11 +179,10 @@ export default function NewBlogs() {
                         control={control}
                         rules={{ required: "Content is required" }}
                         render={({ field: { onChange, value } }) => (
-                            <ReactQuill
+                            <RichTextEditor
                                 value={value}
                                 onChange={onChange}
                                 placeholder='Write something...'
-                                theme='snow'
                                 className='w-full border border-gray-400 rounded-lg focus:outline-none'
                             />
                         )}
diff --git a/src/components/admin/content-management/About/Abouts.tsx b/src/components/admin/content-management/About/Abouts.tsx
index bddd56e..c5ae703 100644
--- a/src/components/admin/content-management/About/Abouts.tsx
+++ b/src/components/admin/content-management/About/Abouts.tsx
@@ -2,8 +2,6 @@
 import { useEffect } from "react";
 import { useDispatch, useSelector } from "react-redux";
 import { useForm, Controller } from "react-hook-form";
-import dynamic from "next/dynamic";
-import "react-quill/dist/quill.snow.css";
 import { RootState } from "@/store/store";
 import {
     createAbout,
@@ -12,7 +10,7 @@ import {
 } from "@/store/features/admin/aboutSlice";
 import ImageUploader from "./AboutImageUploader";
 import { toast } from "react-toastify";
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 interface AboutFormData {
     title: string;
@@ -151,11 +149,10 @@ export default function Abouts() {
                         control={control}
                         rules={{ required: "Content is required" }}
                         render={({ field: { onChange, value } }) => (
-                            <ReactQuill
+                            <RichTextEditor
                                 value={value}
                                 onChange={onChange}
                                 placeholder='Write something...'
-                                theme='snow'
                                 className='w-full border border-gray-400 rounded-lg focus:outline-none'
                             />
                         )}
diff --git a/src/components/admin/content-management/FAQs/FAQs.tsx b/src/components/admin/content-management/FAQs/FAQs.tsx
index 0564ca0..a79ef2d 100644
--- a/src/components/admin/content-management/FAQs/FAQs.tsx
+++ b/src/components/admin/content-management/FAQs/FAQs.tsx
@@ -13,10 +13,8 @@ import { FaEdit, FaTrashAlt, FaEye } from "react-icons/fa";
 import CustomModelAdmin from "../../../modal/CustomModelAdmin";
 import CustomTable from "@/components/custom-table/CustomTable";
 import { exportCsvFile } from "@/utils/exportCsvFile";
-import dynamic from "next/dynamic";
-import "react-quill/dist/quill.snow.css";
 import { toast } from "react-toastify";
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 export interface FAQ {
     _id?: string;
@@ -137,11 +135,10 @@ const ModalFAQs = memo(
                                 dangerouslySetInnerHTML={{ __html: answer }}
                             />
                         ) : (
-                            <ReactQuill
+                            <RichTextEditor
                                 value={answer}
                                 onChange={setAnswer}
                                 placeholder='Write answer...'
-                                theme='snow'
                                 className='w-full border border-gray-400 rounded-lg focus:outline-none'
                             />
                         )}
diff --git a/src/components/admin/content-management/FAQs/NewFAQModal.tsx b/src/components/admin/content-management/FAQs/NewFAQModal.tsx
index f10052e..e7e203a 100644
--- a/src/components/admin/content-management/FAQs/NewFAQModal.tsx
+++ b/src/components/admin/content-management/FAQs/NewFAQModal.tsx
@@ -1,7 +1,5 @@
 import { useState } from "react";
-import dynamic from "next/dynamic";
-import "react-quill/dist/quill.snow.css";
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 export default function ModalFAQs() {
     const [description, setDescription] = useState("");
@@ -29,11 +27,10 @@ export default function ModalFAQs() {
             {/* Description */}
             <div className='mt-4'>
                 <label className='block text-sm font-semibold'>Answer</label>
-                <ReactQuill
+                <RichTextEditor
                     value={description}
                     onChange={handleDescriptionChange}
                     placeholder='Write something...'
-                    theme='snow'
                     className='w-full border border-gray-400 rounded-lg focus:outline-none'
                 />
             </div>
diff --git a/src/components/admin/content-management/HelpAndSupport/NewHelpSupportModal.tsx b/src/components/admin/content-management/HelpAndSupport/NewHelpSupportModal.tsx
index fe944b2..54b7a77 100644
--- a/src/components/admin/content-management/HelpAndSupport/NewHelpSupportModal.tsx
+++ b/src/components/admin/content-management/HelpAndSupport/NewHelpSupportModal.tsx
@@ -1,16 +1,13 @@
 import { useEffect } from "react";
 import { useForm, Controller } from "react-hook-form";
 import { useDispatch, useSelector } from "react-redux";
-import dynamic from "next/dynamic";
 import { RootState, AppDispatch } from "@/store/store";
 import {
     createHelpSupport,
     updateHelpSupport,
 } from "@/store/features/admin/helpSlice";
-import "react-quill/dist/quill.snow.css";
 import { toast } from "react-toastify";
-
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 interface HelpSupportFormData {
     title: string;
@@ -159,10 +156,9 @@ export const ModalCenters: React.FC<ModalCentersProps> = ({ onClose }) => {
                         control={control}
                         rules={{ required: "Content is required" }}
                         render={({ field: { onChange, value } }) => (
-                            <ReactQuill
+                            <RichTextEditor
                                 value={value || ""}
                                 onChange={onChange}
-                                theme='snow'
                                 className='w-full border border-gray-400 rounded-lg mt-2'
                             />
                         )}
diff --git a/src/components/admin/content-management/LandingPage/LandingPages.tsx b/src/components/admin/content-management/LandingPage/LandingPages.tsx
index 5d448f8..2cec5f9 100644
--- a/src/components/admin/content-management/LandingPage/LandingPages.tsx
+++ b/src/components/admin/content-management/LandingPage/LandingPages.tsx
@@ -1,6 +1,5 @@
 "use client";
 import { useEffect, useState } from "react";
-import dynamic from "next/dynamic";
 import { useForm, Controller } from "react-hook-form";
 import { useDispatch, useSelector } from "react-redux";
 import { AppDispatch, RootState } from "@/store/store";
@@ -8,10 +7,8 @@ import {
     fetchLandingPage,
     updateLandingPage,
 } from "@/store/features/admin/lanPageSlice";
-import "react-quill/dist/quill.snow.css";
 import { toast } from "react-toastify";
-
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 interface FormData {
     carouselHeroTitle: string;
@@ -32,7 +29,7 @@ export default function LandingPages() {
         reset,
         setValue,
         watch,
-        formState: { errors },
+        formState: { /* errors */ },
     } = useForm<FormData>({
         defaultValues: {
             carouselHeroTitle: "",
@@ -47,7 +44,7 @@ export default function LandingPages() {
     useEffect(() => {
         dispatch(fetchLandingPage())
             .unwrap()
-            .catch((error) => {
+            .catch((_error) => {
                 toast.error("Failed to fetch landing page data.");
             });
     }, [dispatch]);
@@ -141,7 +138,7 @@ export default function LandingPages() {
                         name='heroSubTitle'
                         control={control}
                         render={({ field: { onChange, value } }) => (
-                            <ReactQuill
+                            <RichTextEditor
                                 value={value}
                                 onChange={(content) => {
                                     onChange(content);
@@ -149,28 +146,6 @@ export default function LandingPages() {
                                         shouldDirty: true,
                                     });
                                 }}
-                                placeholder='Write something...'
-                                theme='snow'
-                                className='w-full border border-gray-400 rounded-lg focus:outline-none'
-                                modules={{
-                                    toolbar: [
-                                        [{ header: [1, 2, false] }],
-                                        [
-                                            "bold",
-                                            "italic",
-                                            "underline",
-                                            "strike",
-                                        ],
-                                        [
-                                            { list: "ordered" },
-                                            { list: "bullet" },
-                                        ],
-                                        ["link", "image"],
-                                        [{ align: [] }],
-                                        [{ color: [] }, { background: [] }],
-                                        ["clean"],
-                                    ],
-                                }}
                             />
                         )}
                     />
diff --git a/src/components/admin/content-management/Terms/CreateTerms.tsx b/src/components/admin/content-management/Terms/CreateTerms.tsx
index 049d3dd..4c1175f 100644
--- a/src/components/admin/content-management/Terms/CreateTerms.tsx
+++ b/src/components/admin/content-management/Terms/CreateTerms.tsx
@@ -1,15 +1,12 @@
 "use client";
 import React, { useState } from "react";
 import { useForm, Controller } from "react-hook-form";
-import dynamic from "next/dynamic";
-import "react-quill/dist/quill.snow.css";
 import { useDispatch } from "react-redux";
 import { AppDispatch } from "@/store/store";
 import { toast } from "react-toastify";
 import { TermsInterface } from "@/types/interfaces";
 import { createTerm } from "@/store/features/admin/termsSlice";
-
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 export function CreateTerms({ onClose }: { onClose: () => void }) {
     const dispatch = useDispatch<AppDispatch>();
@@ -104,11 +101,10 @@ export function CreateTerms({ onClose }: { onClose: () => void }) {
                         control={control}
                         rules={{ required: "Content is required" }}
                         render={({ field: { onChange, value } }) => (
-                            <ReactQuill
+                            <RichTextEditor
                                 value={value}
                                 onChange={onChange}
                                 placeholder='Write something...'
-                                theme='snow'
                                 className='w-full border border-gray-400 rounded-lg focus:outline-none'
                             />
                         )}
diff --git a/src/components/admin/content-management/Terms/EditTerms.tsx b/src/components/admin/content-management/Terms/EditTerms.tsx
index c4b2401..91e348e 100644
--- a/src/components/admin/content-management/Terms/EditTerms.tsx
+++ b/src/components/admin/content-management/Terms/EditTerms.tsx
@@ -1,10 +1,7 @@
-import React, { useEffect, useState } from "react";
-import dynamic from "next/dynamic";
-import "react-quill/dist/quill.snow.css";
+import React, { useEffect } from "react";
 import { TermsInterface } from "@/types/interfaces";
 import { useForm, Controller } from "react-hook-form";
-
-const ReactQuill = dynamic(() => import("react-quill"), { ssr: false });
+import RichTextEditor from "@/components/common/RichTextEditor";
 
 interface TermEditModelProps {
     termData: TermsInterface;
@@ -75,11 +72,10 @@ export function EditTerms({ termData, onClose, onSubmit }: TermEditModelProps) {
                         control={control}
                         name='pageContent'
                         render={({ field }) => (
-                            <ReactQuill
-                                {...field}
+                            <RichTextEditor
+                                value={field.value}
                                 onChange={handleDescriptionChange}
                                 placeholder='Write something...'
-                                theme='snow'
                                 className='w-full border border-gray-400 rounded-lg focus:outline-none'
                             />
                         )}
diff --git a/src/components/common/RichTextEditor.tsx b/src/components/common/RichTextEditor.tsx
new file mode 100644
index 0000000..2e8bb77
--- /dev/null
+++ b/src/components/common/RichTextEditor.tsx
@@ -0,0 +1,106 @@
+"use client";
+
+import { useEffect, useState, useRef } from "react";
+import "react-quill/dist/quill.snow.css";
+
+// Define props interface
+interface RichTextEditorProps {
+  value: string;
+  onChange: (content: string) => void;
+  placeholder?: string;
+  className?: string;
+  modules?: any;
+}
+
+// Create a placeholder component for server-side rendering
+const EditorPlaceholder = () => (
+  <div className="h-64 w-full bg-gray-100 animate-pulse rounded-md flex items-center justify-center text-gray-500">
+    Loading editor...
+  </div>
+);
+
+// The main editor component
+const RichTextEditor = ({
+  value,
+  onChange,
+  placeholder = "Write something...",
+  className = "w-full border border-gray-400 rounded-lg focus:outline-none",
+  modules
+}: RichTextEditorProps) => {
+  // Create refs and state
+  const editorRef = useRef<HTMLDivElement>(null);
+  const [quill, setQuill] = useState<any>(null);
+  const [isClient, setIsClient] = useState(false);
+
+  // Default modules configuration
+  const defaultModules = {
+    toolbar: [
+      [{ header: [1, 2, false] }],
+      ["bold", "italic", "underline", "strike"],
+      [{ list: "ordered" }, { list: "bullet" }],
+      ["link", "image"],
+      [{ align: [] }],
+      [{ color: [] }, { background: [] }],
+      ["clean"],
+    ],
+  };
+
+  // Initialize Quill on the client side only
+  useEffect(() => {
+    setIsClient(true);
+
+    if (typeof window !== 'undefined') {
+      // Dynamically import Quill only on the client side
+      import('quill').then((Quill) => {
+        if (!quill && editorRef.current) {
+          const editor = new Quill.default(editorRef.current, {
+            theme: 'snow',
+            placeholder: placeholder,
+            modules: modules || defaultModules
+          });
+
+          // Set initial content
+          if (value) {
+            editor.clipboard.dangerouslyPasteHTML(value);
+          }
+
+          // Handle content changes
+          editor.on('text-change', () => {
+            const html = editorRef.current?.querySelector('.ql-editor')?.innerHTML || '';
+            onChange(html);
+          });
+
+          setQuill(editor);
+        }
+      });
+    }
+
+    // Cleanup
+    return () => {
+      if (quill) {
+        // Clean up Quill instance if needed
+      }
+    };
+  }, []);
+
+  // Update content when value prop changes
+  useEffect(() => {
+    if (quill && value !== undefined && editorRef.current?.querySelector('.ql-editor')?.innerHTML !== value) {
+      quill.clipboard.dangerouslyPasteHTML(value);
+    }
+  }, [value, quill]);
+
+  // Show placeholder during SSR or while loading
+  if (!isClient) {
+    return <EditorPlaceholder />;
+  }
+
+  // Render the editor container
+  return (
+    <div className={className}>
+      <div ref={editorRef} className="min-h-[200px]"></div>
+    </div>
+  );
+};
+
+export default RichTextEditor;
diff --git a/src/store/store.ts b/src/store/store.ts
index ac45dc4..852280f 100644
--- a/src/store/store.ts
+++ b/src/store/store.ts
@@ -41,6 +41,19 @@ const persistConfig = {
   storage,
   whitelist: ["login"], // Persist only login state
 };
+// const createNoopStorage = () => {
+//   return {
+//     getItem() {
+//       return Promise.resolve(null);
+//     },
+//     setItem() {
+//       return Promise.resolve();
+//     },
+//     removeItem() {
+//       return Promise.resolve();
+//     }
+//   };
+// };
 
 // ???? Combine reducers
 const rootReducer = combineReducers({

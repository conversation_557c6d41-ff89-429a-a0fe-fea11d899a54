/* TipTap Editor Styles */

/* Basic editor styles */
.ProseMirror {
  min-height: 150px;
  outline: none;
}

.ProseMirror > * + * {
  margin-top: 0.75em;
}

.ProseMirror ul,
.ProseMirror ol {
  padding: 0 1rem;
}

.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
  line-height: 1.1;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

.ProseMirror h1 {
  font-size: 1.75rem;
  font-weight: 700;
}

.ProseMirror h2 {
  font-size: 1.5rem;
  font-weight: 600;
}

.ProseMirror code {
  background-color: rgba(97, 97, 97, 0.1);
  color: #616161;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
}

.ProseMirror pre {
  background: #0d0d0d;
  color: #fff;
  font-family: 'JetBrainsMono', monospace;
  padding: 0.75rem 1rem;
  border-radius: 0.5rem;
}

.ProseMirror pre code {
  color: inherit;
  padding: 0;
  background: none;
  font-size: 0.8rem;
}

.ProseMirror img {
  max-width: 100%;
  height: auto;
  margin: 0.5rem 0;
}

.ProseMirror blockquote {
  padding-left: 1rem;
  border-left: 2px solid rgba(13, 13, 13, 0.1);
  margin-left: 0;
  margin-right: 0;
}

.ProseMirror hr {
  border: none;
  border-top: 2px solid rgba(13, 13, 13, 0.1);
  margin: 1.5rem 0;
}

/* Placeholder styles */
.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}

/* Text alignment */
.ProseMirror .text-left {
  text-align: left;
}

.ProseMirror .text-center {
  text-align: center;
}

.ProseMirror .text-right {
  text-align: right;
}

/* Link styles */
.ProseMirror a {
  color: #0074d9;
  text-decoration: underline;
}

/* List styles */
.ProseMirror ul li {
  list-style-type: disc;
}

.ProseMirror ol li {
  list-style-type: decimal;
}

/* Focus styles */
.ProseMirror:focus {
  outline: none;
}

/* Read-only styles */
.ProseMirror[contenteditable="false"] {
  opacity: 0.9;
  cursor: default;
}

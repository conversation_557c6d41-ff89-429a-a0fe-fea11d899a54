
@import "~slick-carousel/slick/slick.css";
@import "~slick-carousel/slick/slick-theme.css";
@import "~react-toastify/dist/ReactToastify.min.css";
/* @import "~react-quill/dist/quill.snow.css"; */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* React Quill custom styles */
.react-quill-wrapper {
  display: flex;
  flex-direction: column;
}

.react-quill-wrapper .quill {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.react-quill-wrapper .ql-container {
  flex: 1;
  overflow: auto;
}

/* Add to your globals.css or equivalent */
.ProseMirror {
  outline: none;
}

.ProseMirror p.is-editor-empty:first-child::before {
  color: #adb5bd;
  content: attr(data-placeholder);
  float: left;
  height: 0;
  pointer-events: none;
}



@font-face {
    font-family: "Shoika";
    src: url("/fonts/Montserrat/static/Montserrat-Regular.ttf")
        format("truetype");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "Shoika";
    src: url("/fonts/Montserrat/static/Montserrat-Bold.ttf") format("truetype");
    font-weight: 700;
    font-style: normal;
}


html {
    scroll-behavior: smooth;
}

body {
    font-family: "Shoika", sans-serif;
    font-weight: 400;
}

.custom-checkbox {
    appearance: none;
    width: 18px;
    height: 18px;
    background-image: url("/close.png");
    background-size: cover;
    cursor: pointer;
    display: inline-block;
}

.custom-checkbox:checked {
    background-image: url("/check.png");
}

.custom-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: white;
    border: 1px solid #ccc;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    z-index: 1;
}

.left-arrow {
    left: -50px;
}

.right-arrow {
    right: -100px;
}

.custom-arrow:hover {
    background-color: #f0f0f0;
}

.tdbgColor {
    background-color: #f6f6f6;
}

.comparison-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    min-width: 100%;
    margin: 0 auto;
}

button.cta-button {
    background-color: rgb(21, 21, 86);
    color: white;
    padding: 15px 30px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 18px;
    font-weight: bold;
    margin-top: 20px;
}

.navbarText {
    font-size: 17.2592px;
    font-weight: 700;
    color: #545454;
    line-height: 24.0395px;
    letter-spacing: normal;
}

.headingText {
    font-size: 43.1479px;
    font-weight: 900;
    color: #000000;
    line-height: 47.1545px;
    letter-spacing: -0.431479px;
}

.headingTextBlue {
    font-size: 43.1479px;
    font-weight: 900;
    color: #4d4ec9;
    line-height: 47.1545px;
    letter-spacing: -0.431479px;
}

.cardTextBlue {
    font-size: 18.1479px;
    font-weight: 900;
    color: #4d4ec9;
    line-height: 47.1545px;
    letter-spacing: -0.431479px;
}

.headingTextBlueSmall {
    font-size: 25.1479px;
    font-weight: 900;
    color: #4d4ec9;
    line-height: 47.1545px;
    letter-spacing: -0.431479px;
}

.headingTextWhite {
    font-size: 43.1479px;
    font-weight: 900;
    color: white;
    line-height: 47.1545px;
    letter-spacing: -0.431479px;
}

.headingTextTwo {
    font-size: 24.656px;
    font-weight: 900;
    color: #000000;
    line-height: 26.8133px;
    letter-spacing: -0.431479px;
}

.paraText {
    font-style: normal;
    font-size: 24.656px;
    font-weight: 400;
    color: #000000;
    line-height: 26.8133px;
}

.paraTextTwo {
    font-style: normal;
    font-size: 17.2592px;
    font-weight: 400;
    color: #000000;
    line-height: 18.4919px;
}

.imageRotate {
    margin-top: -25px;
    transform: rotate(12.06892278deg);
}

.button {
    background-color: #4041a8;
    color: white;
    padding-top: 32px;
    padding-bottom: 32px;
    font-style: normal;
    font-size: 17.2592px;
    font-weight: 900;
    line-height: 18.4919px;
}

.imgHeight {
    height: 15px;
    width: 15px;
}

.cardColor {
    background: linear-gradient(to right, #4647b6, #262663);
    height: 28%;
}
.cardColorTwo {
    background: linear-gradient(to right, #4647b6, #262663);
}

.cardColorChild {
    background-color: #262663;
}

.cardTextWhite {
    font-size: 14.7936px;
    line-height: 20.3411px;
    font-weight: 500;
    color: white;
}

.cardTextWhiteSmall {
    font-size: 10.7936px;
    line-height: 20.3411px;
    font-weight: 900;
    color: white;
}

.cardTextBlack {
    font-size: 14.7936px;
    line-height: 20.3411px;
    font-weight: 500;
    color: black;
}

.cardButton {
    background-color: #4647b6;
}

.cardBorder {
    border: 2px solid #4647b6;
    border-radius: 27px;
}

.yellowGradient {
    background: linear-gradient(90deg, #fff5af 0%, #ffb0f2 100%);
}

.Button {
    background-color: #4d4ec9;
    border-radius: 8px;
}

.ButtonBlue {
    background-color: #4d4ec9;
}

.BlueBorder {
    border-color: #4d4ec9;
}

.BlueBg {
    background-color: #4d4ec9;
}

.BlueText {
    color: #4d4ec9;
}

.gradient-border {
    position: relative;
    padding: 10px;
    background: #fff;
    border-radius: 25px;
}

.gradient-border::before {
    content: "";
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(90deg, #fff5af 0%, #ffb0f2 100%);
    z-index: -1;
    border-radius: 25px;
}

.sectionBG {
    background-color: #e5e1f3;
}

.pinkBorder {
    border-color: #e5e1f3;
}

.sectionBackground {
    background-color: #f5f6f8;
}

.cardButton::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    border: 3px solid #4d4ec9;
}

.cardButton::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    border: 3px solid #4d4ec9;
}

.authBG {
    background-color: #f5f6f8;
}

.authBG2 {
    background-color: #fae9f2;
}

.blogPink {
    background: linear-gradient(
        91.96deg,
        rgba(74, 14, 251, 0.65) 5.54%,
        #a487fb 85.6%
    );
}

input[type="range"]::-webkit-slider-thumb {
    pointer-events: all;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #4d4ec9;
    cursor: pointer;
    -webkit-appearance: none;
}

input[type="range"]::-moz-range-thumb {
    pointer-events: all;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #fff;
    border: 2px solid #4d4ec9;
    cursor: pointer;
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 0 0% 3.9%;
        --card: 0 0% 100%;
        --card-foreground: 0 0% 3.9%;
        --popover: 0 0% 100%;
        --popover-foreground: 0 0% 3.9%;
        --primary: 0 0% 9%;
        --primary-foreground: 0 0% 98%;
        --secondary: 0 0% 96.1%;
        --secondary-foreground: 0 0% 9%;
        --muted: 0 0% 96.1%;
        --muted-foreground: 0 0% 45.1%;
        --accent: 0 0% 96.1%;
        --accent-foreground: 0 0% 9%;
        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 89.8%;
        --input: 0 0% 89.8%;
        --ring: 0 0% 3.9%;
        --chart-1: 12 76% 61%;
        --chart-2: 173 58% 39%;
        --chart-3: 197 37% 24%;
        --chart-4: 43 74% 66%;
        --chart-5: 27 87% 67%;
        --radius: 0.5rem;
    }
    .dark {
        --background: 0 0% 3.9%;
        --foreground: 0 0% 98%;
        --card: 0 0% 3.9%;
        --card-foreground: 0 0% 98%;
        --popover: 0 0% 3.9%;
        --popover-foreground: 0 0% 98%;
        --primary: 0 0% 98%;
        --primary-foreground: 0 0% 9%;
        --secondary: 0 0% 14.9%;
        --secondary-foreground: 0 0% 98%;
        --muted: 0 0% 14.9%;
        --muted-foreground: 0 0% 63.9%;
        --accent: 0 0% 14.9%;
        --accent-foreground: 0 0% 98%;
        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 0 0% 98%;
        --border: 0 0% 14.9%;
        --input: 0 0% 14.9%;
        --ring: 0 0% 83.1%;
        --chart-1: 220 70% 50%;
        --chart-2: 160 60% 45%;
        --chart-3: 30 80% 55%;
        --chart-4: 280 65% 60%;
        --chart-5: 340 75% 55%;
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}
